<template>
	<view class="question">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="back-btn" @click="goBack">
				<image src="/static/nav-back-icon.png" class="back-icon"></image>
			</view>
			<text class="title">{{ projectName || 'SNAP-IV父母评定问卷' }}</text>
		</view>
		
		<view class="question-schedule">
			<progress class="question-schedule-icon" :percent="progressPercent" :show-info="false" border-radius="50" activeColor="#41A8DE" stroke-width="8" />
			<view class="question-schedule-text"><text class="question-schedule-text-num">{{currentQuestionIndex}}</text>/{{totalQuestions}}</view>
		</view>
		<view class="question-content">
			<view class="question-content__text">请根据您孩子过去一周内的实际情况选择</view>
			<!-- 加载状态 -->
			<view v-if="loading" class="question-content__loading">
				<text>正在加载题目...</text>
			</view>
			<!-- 题目内容 -->
			<view v-else-if="questions.length > 0" class="question-content__topic">
				<text class="question-content__topic-text">{{currentQuestion.text}}</text>
				<view v-for="option in options" class="question-content__topic-options" :class="[selectedAnswer==option.value?'question-content__topic-options-clicked':'']" :key="option.value"
					@click="selectOption(option)">
					{{option.text}}
				</view>
			</view>
			<!-- 无题目状态 -->
			<view v-else class="question-content__empty">
				<text>暂无题目数据</text>
			</view>
		</view>
		<view class="question-bottom">
			<view class="question-bottom-next center" @click="goToPrevious" v-if="showPreviousButton">
				<text class="question-bottom-next-text">上一题</text>
			</view>
		</view>
		<uni-popup ref="finishPopup" type="center" :animation="false">
			<view class="popup-box" v-if="questionConfig.isFinish">
				<view class="popup-box-top">
					<view class="popup-box-top__text">
						<view class="popup-box-top__text-top">确认提交问卷</view>
					</view>
				</view>
				<view class="popup-box-center">本次问卷已完成，是否提交本次评估结果<view class="">
					</view>
				</view>
				<view class="popup-box__button">
					<view class="popup-box__button-left" @click="cancelSubmit">取消</view>
					<view class="popup-box__button-right" @click="confirmSubmit">确认</view>
				</view>
			</view>
			<view class="popup-box" v-else style="width: 960rpx;">
				<view class="popup-box-top">
					<view class="popup-box-top__text">
						<view class="popup-box-top__text-top">指导语</view>
					</view>
				</view>
				<view class="popup-box-center">
					0分一从来没有；<br />
					1分一有时(偶尔每月几次)；<br />
					2分一经常 (每周几次)；<br />
					3分一非常常见(每天一次或者多次，或者每周几天一天多次)。<br />
					如果评分人在1分与2分之间难以选择时，请选择1分。
				</view>
				<view class="popup-box__button">
					<view class="popup-box__button-only" @click="closeGuide">我已知晓</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script setup>
	import {
		computed,
		onMounted,
		reactive,
		ref
	} from "vue";
	import {
		showToast
	} from "../../common/uniTool";
	import {
		getQuestionItemList,
		subQuestionData
	} from "../../service/scale";
	import {
		onShow,
		onLoad
	} from '@dcloudio/uni-app'
	const finishPopup = ref(null)
	
	const questionConfig = reactive({
		answers: [],
		questionIndex: 1,
		answer: -1,
		isClick: true,
		isEnd: -1,
		isFinish: false
	})

	// 页面参数
	const projectId = ref('')
	const projectName = ref('')
	const workOrderId = ref('')
	const traineeId = ref('')

	// 题目数据
	const itemList = ref([])
	const loading = ref(false)
	
	const options = ref([{
		value: 'A',
		text: '完全没有'
	}, {
		value: 'B',
		text: '有一点点'
	}, {
		value: 'C',
		text: '还算不少'
	}, {
		value: 'D',
		text: '非常的多'
	}])
	
	// 移除硬编码的题目，改为从API获取
	const questions = computed(() => {
		return itemList.value.map(item => item.itemContent) || []
	})
	
	// 计算属性
	const currentQuestionIndex = computed(() => questionConfig.questionIndex)
	const selectedAnswer = computed(() => questionConfig.answer)
	const totalQuestions = computed(() => questions.value.length)
	const progressPercent = computed(() => (questionConfig.questionIndex / questions.value.length) * 100)
	const currentQuestion = computed(() => ({ text: questions.value[questionConfig.questionIndex - 1] || '' }))
	const showPreviousButton = computed(() => questionConfig.questionIndex > 1)
	
	// 使用onLoad生命周期来接收页面参数
	onLoad((options) => {
		if (options.projectId) {
			projectId.value = options.projectId
		}
		if (options.projectName) {
			projectName.value = decodeURIComponent(options.projectName)
		}
		if (options.workOrderId) {
			workOrderId.value = options.workOrderId
		}
		if (options.traineeId) {
			traineeId.value = options.traineeId
		}

		// 如果有projectId，则获取题目列表
		if (projectId.value) {
			loadQuestionItems()
		}
	})

	onMounted(() => {
		// 页面挂载时的逻辑
	})
	
	onShow(() => {
		//#ifdef APP-PLUS
		plus.navigator.setFullscreen(true);
		//#endif
	})

	// 获取题目列表
	const loadQuestionItems = async () => {
		if (!projectId.value) return

		loading.value = true
		try {
			const response = await getQuestionItemList({
				projectId: projectId.value
			})

			if (response.code === '0000' && response.data && response.data.itemList) {
				itemList.value = response.data.itemList
				// 更新选项为实际的选项
				if (itemList.value.length > 0 && itemList.value[0].itemOptionsList) {
					options.value = itemList.value[0].itemOptionsList.map(option => ({
						value: option.optionsCode,
						text: option.optionsDesc
					}))
				}
				// 进入页面不再显示指导语弹窗
			} else {
				showToast('获取题目失败')
			}
		} catch (error) {
			console.error('获取题目列表失败:', error)
			showToast('获取题目失败')
		} finally {
			loading.value = false
		}
	}

	const closeGuide = () => {
		finishPopup.value.close()
		questionConfig.answers.pop()
	}
	
	const confirmSubmit = () => {
		// 准备提交数据
		const serNumList = []
		const answerList = []

		// 解析答案数据
		questionConfig.answers.forEach(answer => {
			const [serNum, answerCode] = answer.split(',')
			serNumList.push(parseInt(serNum))
			answerList.push(answerCode)
		})

		// 使用新的提交接口
		subQuestionData({
			projectId: projectId.value,
			workorderId: workOrderId.value,
			traineeId: traineeId.value,
			serNumList: serNumList,
			answerList: answerList,
			evaluationSourceType: "0" // 测评工单固定为0
		}).then(() => {
			showToast('提交成功')
			// 返回到工单详情页面
			uni.navigateBack({
				delta: 2 // 返回两级，跳过介绍页面
			})
		}).catch(err => {
			showToast(err.msg || '提交失败')
		})
	}
	
	const selectOption = (option) => {
		if (!questionConfig.isClick) {
			return
		}
		questionConfig.answer = option.value
		questionConfig.isClick = false
		setTimeout(() => {
			// 获取当前题目的序号
			const currentItem = itemList.value[questionConfig.questionIndex - 1]
			const itemSerNum = currentItem ? currentItem.itemSerNum : questionConfig.questionIndex

			// 记录答案：题目序号,选项代码
			if (questionConfig.answers.length == questions.value.length) {
				questionConfig.answers[questionConfig.questionIndex - 1] = `${itemSerNum},${option.value}`
			} else {
				questionConfig.answers.push(`${itemSerNum},${option.value}`)
			}

			if (questionConfig.questionIndex >= questions.value.length) {
				questionConfig.isFinish = true
				finishPopup.value.open('center')
				questionConfig.answer = option.value
				questionConfig.isClick = true
				return
			}
			questionConfig.questionIndex += 1
			questionConfig.answer = -1
			questionConfig.isClick = true
		}, 250)
	}
	
	const goToPrevious = () => {
		questionConfig.questionIndex -= 1
		// 获取上一题的答案
		const previousAnswer = questionConfig.answers[questionConfig.questionIndex - 1]
		if (previousAnswer) {
			questionConfig.answer = previousAnswer.split(',')[1]
		}
		questionConfig.answers.pop()
	}
	
	const cancelSubmit = () => {
		finishPopup.value.close()
		questionConfig.answers.pop()
	}
	
	const goBack = () => {
		uni.navigateBack()
	}
</script>

<style lang="scss">
	.popup-box {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: space-between;
		width: 740rpx;
		background: #ffffff;
		border-radius: 20rpx;
		padding: 0 40rpx;

		&-top {
			width: 100%;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: space-around;

			&__icon {
				width: 160rpx;
				height: 160rpx;
			}

			&__text {
				display: flex;
				flex-direction: column;
				align-items: center;

				&-top {
					font-size: 48rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: 600;
					color: #111111;
					margin-bottom: 80rpx;
					margin-top: 80rpx;
				}
			}
		}

		&-center {
			font-size: 32rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #666666;
			line-height: 48rpx;
			margin-bottom: 72rpx;
		}

		&__button {
			width: 100%;
			height: 100%;
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 40rpx;

			&-left {
				width: 260rpx;
				height: 120rpx;
				background: rgba($color: #41A8DE, $alpha: 0.1);
				border-radius: 24rpx;
				font-size: 40rpx;
				font-weight: 600;
				color: #41A8DE;
				text-align: center;
				line-height: 120rpx;
			}

			&-right {
				width: 260rpx;
				height: 120rpx;
				background: #41A8DE;
				border-radius: 24rpx;
				font-size: 40rpx;
				font-weight: 600;
				color: #ffffff;
				text-align: center;
				line-height: 120rpx;
			}

			&-only {
				width: 560rpx;
				height: 120rpx;
				background: #41A8DE;
				border-radius: 24rpx;
				font-size: 40rpx;
				font-family: PingFangSC-Semibold, PingFang SC;
				font-weight: 600;
				color: #ffffff;
				text-align: center;
				line-height: 120rpx;
				margin: 0 auto;
			}
		}
	}

	.question {
		width: 100vw;
		height: 100vh;
		background: #f6f6f6;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: flex-start;
		position: relative;

		/* 顶部导航栏 */
		.header {
			position: relative;
			width: 100%;
			height: 82px;
			background: #FFFFFF;
			flex-shrink: 0;
			display: flex;
			align-items: center;
			justify-content: center;
			
			.back-btn {
				position: absolute;
				left: 37px;
				width: 30px;
				height: 30px;
				display: flex;
				align-items: center;
				justify-content: center;
				
				.back-icon {
					width: 24px;
					height: 24px;
				}
			}
			
			.title {
				font-family: 'Alibaba PuHuiTi', 'PingFang SC';
				font-size: 30px;
				color: #333333;
				line-height: 30px;
				font-weight: 600;
			}
		}

		&-schedule {
			display: flex;
			width: 100%;
			flex-direction: column;
			align-items: center;

			&-icon {
				width: 100%;
			}

			&-text {
				font-size: 24rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #999999;
				margin-top: 40rpx;

				&-num {
					font-size: 48rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: 500;
					color: #111111;
				}
			}
		}

		&-content {
			display: flex;
			flex-direction: column;
			align-items: center;
			width: 100%;
			flex: 1;

			&__text {
				font-size: 28rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #999999;
				margin-bottom: 40rpx;
			}

			&__loading, &__empty {
				width: 88%;
				background: #ffffff;
				border-radius: 32rpx;
				padding: 80rpx 40rpx;
				display: flex;
				justify-content: center;
				align-items: center;

				text {
					font-size: 32rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #999999;
				}
			}

			&__topic {
				width: 88%;
				background: #ffffff;
				border-radius: 32rpx;
				padding: 40rpx 40rpx 40rpx 40rpx;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				align-items: center;

				&-text {
					font-size: 40rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: 500;
					color: #111111;
					line-height: 56rpx;
					height: 128rpx;
					display: flex;
					align-items: center;
				}

				&-options {
					width: 590rpx;
					height: 120rpx;
					margin-bottom: 20rpx;
					background: #f6f6f6;
					border-radius: 60rpx;
					text-align: center;
					line-height: 120rpx;
					color: #111111;
					font-size: 36rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: bolder;


					&-clicked {
						background: #41A8DE;
						color: #FFFFFF;
					}
				}
			}
		}

		&-bottom {
			width: 670rpx;
			height: 120rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 40rpx;

			&-next {
				width: 510rpx;
				height: 100rpx;
				background: #f6f6f6;
				border-radius: 60rpx;
				border: 2rpx solid #41A8DE;
				margin: 0 auto;

				&-text {
					font-size: 36rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #41A8DE;
				}
			}

			&-one {
				width: 100%;
				height: 100%;
				background: #41A8DE;
				border-radius: 24rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			&-left {
				width: 315rpx;
				height: 120rpx;
				background: #e5f0e6;
				border-radius: 24rpx;
				display: flex;
				align-items: center;
				justify-content: center;

				&-text {
					font-size: 40rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: 600;
					color: #41A8DE;
				}
			}

			&-right {
				width: 315rpx;
				height: 120rpx;
				background: #41A8DE;
				border-radius: 24rpx;
				display: flex;
				align-items: center;
				justify-content: center;

				&-text {
					font-size: 40rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: 600;
					color: #ffffff;
				}
			}
		}
	}
</style>
